import { ColumnDef } from "@tanstack/react-table"

import { FilterItem, SortingItem } from "@/components/toolbar/types"

import { TextColumn } from "../types"

type GenerateTextConfigurationParams = {
  column: TextColumn
}

type GenerateTextConfigurationReturn = {
  column: ColumnDef<any, any>
  filter?: FilterItem
  sorting?: SortingItem
}

export function generateTextConfiguration(
  params: GenerateTextConfigurationParams
): GenerateTextConfigurationReturn {
  const { cell, header, accessorKey, enableSorting, enableFiltering } =
    params.column

  let filter: FilterItem | undefined
  let sorting: SortingItem | undefined

  if (enableFiltering) {
    filter = {
      accessorKey,
      label: header,
      variant: "text",
    }
  }

  if (enableSorting) {
    sorting = {
      accessorKey,
      label: header,
    }
  }

  const column: ColumnDef<any, any> = {
    id: accessorKey,
    accessorKey,
    header,
    cell: ({ row, getValue }) => {
      const value = getValue()
      return !!cell ? cell({ row, value }) : value
    },
  }

  return {
    column,
    filter,
    sorting,
  }
}
