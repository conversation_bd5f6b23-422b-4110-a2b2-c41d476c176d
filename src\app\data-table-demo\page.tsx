import React from "react"
import { Admin } from "@/db/generated"
import prisma from "@/db/prisma"
import { SearchParams } from "nuqs/server"

import { DataTable } from "@/components/data-table"
import { getPaginationQueryParser } from "@/components/pagination"

export default async function DataTableDemoPage({
  searchParams,
}: {
  searchParams: Promise<SearchParams>
}) {
  const adminsCount = await prisma.admin.count()

  const { skip, take } = await getPaginationQueryParser(searchParams, {
    itemsCount: adminsCount,
    defaultLimit: 10,
    limitOptions: [5, 10, 20, 30, 40, 50],
  })

  const data = await prisma.admin.findMany({
    skip,
    take,
  })

  return (
    <div>
      <DataTable<Admin>
        data={data}
        dataCount={adminsCount}
        columns={[
          {
            variant: "text",
            accessorKey: "id",
            header: "ID",
            enableFiltering: true,
          },
          {
            variant: "text",
            accessorKey: "name",
            header: "Name",
            enableFiltering: true,
          },
          {
            variant: "text",
            accessorKey: "email",
            header: "Email",
            enableFiltering: true,
          },
        ]}
      />
    </div>
  )
}
