import * as React from "react"
import { parseAsInteger, useQueryState } from "nuqs"

import { PaginationContext } from "../contexts"
import { getPageCount } from "../lib/pagination-lib"

export function usePaginationState() {
  const context = React.useContext(PaginationContext)
  if (!context) throw new Error("Error From usePaginationState")

  const {
    dir,
    locale,
    itemsCount,
    defaultLimit,
    limitOptions,
    startTransition,
  } = context

  const [page, setPage] = useQueryState(
    "page",
    parseAsInteger.withDefault(1).withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  const [limit, setLimit] = useQueryState(
    "limit",
    parseAsInteger.withDefault(defaultLimit).withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  const pageCount = React.useMemo(() => {
    return getPageCount(itemsCount, limit)
  }, [itemsCount, limit])

  const canPreviousPage = page > 1
  const canNextPage = page < pageCount

  const goToFirstPage = React.useCallback(() => {
    if (!canPreviousPage) return
    setPage(1)
  }, [canPreviousPage, setPage])

  const goToLastPage = React.useCallback(() => {
    if (!canNextPage) return
    setPage(pageCount)
  }, [canNextPage, pageCount, setPage])

  const goToPreviousPage = React.useCallback(() => {
    if (!canPreviousPage) return
    setPage((prev) => prev - 1)
  }, [canPreviousPage, setPage])

  const goToNextPage = React.useCallback(() => {
    if (!canNextPage) return
    setPage((prev) => prev + 1)
  }, [canNextPage, setPage])

  const updateLimit = React.useCallback(
    (value: number | string) => {
      const limit = Number(value)
      if (!limitOptions.includes(limit)) return
      setLimit(limit)
    },
    [limitOptions, setLimit]
  )

  return {
    locale,
    dir,
    page,
    limit,
    pageCount,
    canNextPage,
    limitOptions,
    canPreviousPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
    updateLimit,
  }
}
