// pagination-types.ts

import React from "react"

import { Locale } from "@/types/globals"

import type { paginationConfig } from "../configs"

export type PaginationConfig = typeof paginationConfig

export type PaginationContextValue = Required<
  Omit<PaginationProps, "startTransition" | "className" | "children">
> & {
  startTransition?: React.TransitionStartFunction
}

export type PaginationProps = {
  /**
   * العدد الإجمالي للعناصر
   */
  itemsCount: number
  /**
   * مصفوفة تحتوي على خيارات عدد العناصر في كل صفحة
   *
   * @default [10, 20, 30, 40, 50]
   */
  limitOptions?: number[]
  /**
   * عدد العناصر الافتراضي في كل صفحة
   *
   * @default 10
   */
  defaultLimit?: number
  /**
   * Callback function to start a transition.
   *
   * @example
   * ```tsx
   * const [isPending, startTransition] = useTransition()
   *
   * <Pagination startTransition={startTransition}>
   *   ...
   * </Pagination>
   * ```
   */
  startTransition?: React.TransitionStartFunction
  /**
   * The direction of the pagination.
   *
   * @default "rtl"
   */
  dir?: "rtl" | "ltr"
  /**
   * The locale of the pagination.
   *
   * @default "ar"
   */
  locale?: Locale
  className?: string
  children?: React.ReactNode
}
