"use client"

import * as React from "react"

import { ToolbarContext } from "./contexts"
import { getToolbarDictionary } from "./lib"
import { ToolbarProps } from "./types"

export function Toolbar({
  children,
  className,
  locale = "ar",
  dir = "rtl",
  startTransition,
}: ToolbarProps) {
  const dictionaries = React.useMemo(
    () => getToolbarDictionary(locale),
    [locale]
  )

  return (
    <ToolbarContext value={{ startTransition, dictionaries, dir }}>
      <div className={className} dir={dir}>
        {children}
      </div>
    </ToolbarContext>
  )
}

export { getToolbarQueryParser } from "./lib/toolbar-lib"
