// data-table-types.ts

import type React from "react"

import { DynamicObject } from "@/types/globals"

export type ColumnVariant =
  | "text"
  | "number"
  | "array"
  | "json"
  | "boolean"
  | "date"
  | "options"
  | "multiOptions"
  | "link"
  | "image"
  | "video"
  | "audio"

interface BaseColumn<K extends string = string> {
  accessorKey: K
  header: string
}

export type TextColumn<T extends DynamicObject = DynamicObject> = {
  [K in keyof T]: {
    variant: "text"
    enableSorting?: boolean
    enableFiltering?: boolean
    filter?: { placeholder?: string }
    cell?: (props: { row: T; value: T[K] }) => React.ReactNode
  } & BaseColumn<Extract<K, string>>
}[keyof T]

export type DataTableColumn<T extends DynamicObject = DynamicObject> = {
  [K in keyof T]: TextColumn<T>
}[keyof T]

export type DataTableProps<T extends DynamicObject = DynamicObject> = {
  data: T[]
  dataCount: number
  columns: DataTableColumn<T>[]
  className?: string
}

export type DataTableContextValue<T extends DynamicObject = DynamicObject> =
  Omit<DataTableProps<T>, "className">
