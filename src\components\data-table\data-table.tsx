"use client"

import * as React from "react"
import { flexRender } from "@tanstack/react-table"

import { DynamicObject } from "@/types/globals"
import { cn } from "@/lib/utils"

import {
  Pagination,
  PaginationButtons,
  PaginationInfo,
  PaginationLimitSelector,
} from "../pagination"
import { <PERSON><PERSON><PERSON>, Tool<PERSON><PERSON>ilter, ToolbarSorting } from "../toolbar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table"
import { DataTableContext } from "./contexts"
import { useDataTable } from "./hooks"
import { DataTableProps } from "./types"

export function DataTable<T extends DynamicObject = DynamicObject>(
  props: DataTableProps<T>
) {
  const { data, columns, dataCount } = props

  return (
    <DataTableContext value={{ data, columns, dataCount }}>
      <div className="flex w-full flex-col gap-2.5 overflow-auto">
        <DataTableToolbar />

        <div className="overflow-hidden rounded-md border">
          <Table>
            <DataTableHeader />
            <DataTableBody />
          </Table>
        </div>

        <DataTablePagination />
      </div>
    </DataTableContext>
  )
}

function DataTableHeader() {
  const { table } = useDataTable()

  return (
    <TableHeader>
      {table.getHeaderGroups().map((headerGroup) => (
        <TableRow className="bg-muted/50" key={headerGroup.id}>
          {headerGroup.headers.map((header) => (
            <TableHead key={header.id} colSpan={header.colSpan}>
              {header.isPlaceholder
                ? null
                : flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
            </TableHead>
          ))}
        </TableRow>
      ))}
    </TableHeader>
  )
}

function DataTableBody() {
  const { table } = useDataTable()

  return (
    <TableBody>
      {table.getRowModel().rows?.length ? (
        table.getRowModel().rows.map((row) => (
          <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
            {row.getVisibleCells().map((cell) => (
              <TableCell key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableCell>
            ))}
          </TableRow>
        ))
      ) : (
        <TableRow>
          <TableCell
            colSpan={table.getAllColumns().length}
            className="h-24 text-center"
          >
            No results.
          </TableCell>
        </TableRow>
      )}
    </TableBody>
  )
}

function DataTableToolbar() {
  const { filters, sorting } = useDataTable()

  return (
    <Toolbar>
      <ToolbarFilter items={filters} />
      <ToolbarSorting items={sorting} />
    </Toolbar>
  )
}

function DataTablePagination() {
  const { dataCount, table } = useDataTable()

  return (
    <div
      dir="rtl"
      className={cn(
        "flex w-full flex-col-reverse items-center justify-between gap-5 overflow-auto p-1 sm:flex-row sm:gap-8"
      )}
    >
      <div className="text-muted-foreground flex-1 text-sm whitespace-nowrap">
        {table.getFilteredSelectedRowModel().rows.length} of{" "}
        {table.getFilteredRowModel().rows.length} row(s) selected.
      </div>
      <Pagination itemsCount={dataCount}>
        <PaginationLimitSelector />
        <PaginationInfo />
        <PaginationButtons />
      </Pagination>
    </div>
  )
}
