import { ColumnDef } from "@tanstack/react-table"

import { FilterItem, SortingItem } from "@/components/toolbar/types"

import { DataTableColumn } from "../types"
import { generateTextConfiguration } from "./genarate-text-configuration"

type GenerateTableConfigurationParams = {
  columns: DataTableColumn[]
}

type GenerateTableConfigurationReturn = {
  columns: ColumnDef<any, any>[]
  filters: FilterItem[]
  sorting: SortingItem[]
}

export function generateTableConfiguration(
  params: GenerateTableConfigurationParams
): GenerateTableConfigurationReturn {
  const columns = new Set<ColumnDef<any, any>>()
  const filters = new Set<FilterItem>()
  const sorting = new Set<SortingItem>()

  for (const column of params.columns) {
    switch (column.variant) {
      case "text":
        const generated = generateTextConfiguration({ column })

        const generatedColumn = generated.column
        const generatedFilter = generated.filter
        const generatedSorting = generated.sorting

        columns.add(generatedColumn)
        if (!!generatedFilter) filters.add(generatedFilter)
        if (!!generatedSorting) sorting.add(generatedSorting)
        break
    }
  }

  return {
    columns: Array.from(columns),
    filters: Array.from(filters),
    sorting: Array.from(sorting),
  }
}
