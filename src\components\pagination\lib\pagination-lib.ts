import { notFound } from "next/navigation"
import {
  createSearchParamsCache,
  parseAsInteger,
  SearchParams,
} from "nuqs/server"

import { paginationConfig } from "../configs"

/**
 * Returns the number of pages for a given number of items.
 *
 * @param itemsCount The number of items.
 *
 * @param perPage The number of items per page.
 *
 * @returns The number of pages.
 *
 * @example
 * ```ts
 * const pageCount = getPageCount(
 *     100, // Number of elements, in other words, the total number of data.
 *     10 // Number of items per page.
 *   )
 *
 * console.log(pageCount) // 10
 * ```
 */
export const getPageCount = (itemsCount: number, perPage: number) => {
  return Math.ceil(itemsCount / perPage)
}

/**
 * Returns the skip and take values for a specific page
 * and boundary and is well suited for use with Prisma queries.
 *
 * @param searchParams The search params from the URL.
 *
 * @param options The options for the pagination.
 *
 * @returns The skip and take values.
 *
 * @example
 * ```ts
 * const { skip, take } = await getPaginationQueryParser(searchParams)
 *
 * console.log(skip) // 0
 * console.log(take) // 10
 * ```
 *
 * Note that if you change the limitOptions in
 * the Pagination component, you must set the same value in
 * Options.limitOptions as you set it for the component.
 *
 * @example
 * ```tsx
 * const { skip, take } = await getPaginationQueryParser(searchParams, {
 *   limitOptions: [5, 15, 20, 25, 30],
 *   defaultLimit: 10, // Default value, must be equal to one of the limitOptions values.
 * })
 *
 * // In the Pagination component
 * <Pagination limitOptions={[5, 15, 20, 25, 30]}>
 *   ...
 * </Pagination>
 * ```
 */
export const getPaginationQueryParser = async (
  searchParams: Promise<SearchParams> | SearchParams,
  options?: {
    defaultLimit?: number
    limitOptions?: number[]
    itemsCount?: number
  }
) => {
  const {
    defaultLimit = paginationConfig.defaultLimit,
    limitOptions = paginationConfig.limitOptions,
    itemsCount = undefined,
  } = options ?? {}

  const searchParamsCache = createSearchParamsCache({
    page: parseAsInteger.withDefault(1),
    limit: parseAsInteger.withDefault(defaultLimit),
  })

  const searchParamsCacheParsed = searchParamsCache.parse(await searchParams)

  const page = searchParamsCacheParsed.page
  const limit = searchParamsCacheParsed.limit

  if (!limitOptions.includes(limit)) {
    throw new Error(
      `لا يمكنك تعيين قيمة عدد العناصر في الصفحة بقيمة لا تساوي احد الخيارات المسموح بها ${limitOptions.join(", ")}`
    )
  }

  return {
    skip: ((page <= 0 ? 1 : page) - 1) * limit,
    take: limit,
  }
}
