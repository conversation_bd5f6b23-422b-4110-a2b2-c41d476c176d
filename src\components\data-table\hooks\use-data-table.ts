import * as React from "react"
import { getCoreRowModel, useReactTable } from "@tanstack/react-table"

import { DataTableContext } from "../contexts"
import { generateTableConfiguration } from "../lib/generate-table-configuration"

export function useDataTable() {
  const context = React.useContext(DataTableContext)
  if (!context) throw new Error("Error From useDataTable")

  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState({})

  const data = React.useMemo(() => context.data, [context.data])
  const dataCount = React.useMemo(() => context.dataCount, [context.dataCount])

  const { columns, filters, sorting } = React.useMemo(
    () => generateTableConfiguration({ columns: context.columns }),
    [context.columns]
  )

  const table = useReactTable({
    data,
    columns,
    enableRowSelection: true,
    state: { rowSelection, columnVisibility },
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
  })

  return { table, filters, sorting, dataCount }
}
