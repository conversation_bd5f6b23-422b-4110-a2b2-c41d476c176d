"use client"

import * as React from "react"
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeftIcon,
  ChevronsRightIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "../ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { paginationConfig } from "./configs"
import { PaginationContext } from "./contexts"
import { usePaginationState } from "./hooks"
import { PaginationProps } from "./types"

export function Pagination({
  itemsCount,
  defaultLimit = paginationConfig.defaultLimit,
  limitOptions = paginationConfig.limitOptions,
  className,
  children,
  dir = "rtl",
  locale = "ar",
  startTransition,
}: PaginationProps) {
  return (
    <PaginationContext
      value={{
        itemsCount,
        defaultLimit,
        limitOptions,
        startTransition,
        dir,
        locale,
      }}
    >
      <div
        className={cn(
          "flex flex-col-reverse items-center gap-5 sm:flex-row sm:gap-6 lg:gap-8",
          className
        )}
        dir={dir}
      >
        {children}
      </div>
    </PaginationContext>
  )
}

export function PaginationLimitSelector({ className }: { className?: string }) {
  const { dir, locale, limitOptions, limit, updateLimit } = usePaginationState()

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <p className="text-sm font-medium whitespace-nowrap">
        {locale === "ar" ? "العناصر لكل صفحة" : "Items per page"}
      </p>
      <Select dir={dir} value={`${limit}`} onValueChange={updateLimit}>
        <SelectTrigger className="h-8 w-[4.5rem] [&[data-size]]:h-8">
          <SelectValue placeholder={limit} />
        </SelectTrigger>
        <SelectContent side="top">
          {limitOptions.map((limitOption) => (
            <SelectItem key={limitOption} value={`${limitOption}`}>
              {limitOption}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

export function PaginationButtons({ className }: { className?: string }) {
  const {
    dir,
    canNextPage,
    canPreviousPage,
    goToFirstPage,
    goToPreviousPage,
    goToNextPage,
    goToLastPage,
  } = usePaginationState()

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Button
        aria-label="Go to first page"
        variant="outline"
        size="icon"
        className="hidden size-8 lg:flex"
        onClick={goToFirstPage}
        disabled={!canPreviousPage}
      >
        <ChevronsRightIcon className={cn({ "rotate-180": dir === "ltr" })} />
      </Button>
      <Button
        aria-label="Go to previous page"
        variant="outline"
        size="icon"
        className="size-8"
        onClick={goToPreviousPage}
        disabled={!canPreviousPage}
      >
        <ChevronRightIcon className={cn({ "rotate-180": dir === "ltr" })} />
      </Button>
      <Button
        aria-label="Go to next page"
        variant="outline"
        size="icon"
        className="size-8"
        onClick={goToNextPage}
        disabled={!canNextPage}
      >
        <ChevronLeftIcon className={cn({ "rotate-180": dir === "ltr" })} />
      </Button>
      <Button
        aria-label="Go to last page"
        variant="outline"
        size="icon"
        className="hidden size-8 lg:flex"
        onClick={goToLastPage}
        disabled={!canNextPage}
      >
        <ChevronsLeftIcon className={cn({ "rotate-180": dir === "ltr" })} />
      </Button>
    </div>
  )
}

export function PaginationInfo({ className }: { className?: string }) {
  const { locale, page, pageCount } = usePaginationState()

  return (
    <div
      className={cn(
        "flex items-center justify-center text-sm font-medium",
        className
      )}
    >
      {locale === "ar"
        ? `الصفحة ${page} من ${pageCount}`
        : `Page ${page} of ${pageCount}`}
    </div>
  )
}
