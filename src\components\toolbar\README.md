<div dir="rtl" >

# مكونات Toolbar - دليل الاستخدام

مجموعة من المكونات المتكاملة لإنشاء شريط أدوات تفاعلي يدعم البحث والتصفية والترتيب مع تزامن حالة URL.

## المكونات الأساسية

### 1. <PERSON><PERSON><PERSON> (المكون الرئيسي)

المكون الحاوي الذي يوفر السياق والإعدادات المشتركة لجميع مكونات شريط الأدوات.

#### الخصائص (Props)

```typescript
interface ToolbarProps {
  children: React.ReactNode
  className?: string
  locale?: "ar" | "en" // اللغة (افتراضي: "ar")
  dir?: "rtl" | "ltr" // الاتجاه (افتراضي: "rtl")
  startTransition?: React.TransitionStartFunction
}
```

#### مثال الاستخدام

```tsx
import { useTransition } from "react"

import { Toolbar } from "@/components/toolbar"

function MyPage() {
  const [isPending, startTransition] = useTransition()

  return (
    <Toolbar startTransition={startTransition} locale="ar" dir="rtl">
      {/* مكونات شريط الأدوات */}
    </Toolbar>
  )
}
```

### 2. ToolbarSearch (البحث)

مكون البحث النصي مع دعم التأخير (debouncing) وتزامن URL.

#### الخصائص (Props)

```typescript
interface SearchProps<T> {
  item: {
    accessorKey: keyof T // مفتاح الحقل المراد البحث فيه
    placeholder?: string // نص المساعدة
  }
  shallow?: boolean // تحديث URL بدون إعادة تحميل (افتراضي: false)
  debounceMs?: number // تأخير التحديث بالملي ثانية (افتراضي: 300)
  className?: string
}
```

#### مثال الاستخدام

```tsx
<ToolbarSearch
  item={{
    accessorKey: "name",
    placeholder: "بحث بالاسم...",
  }}
  debounceMs={500}
/>
```

### 3. ToolbarSorting (الترتيب)

مكون الترتيب متعدد المستويات مع إمكانية السحب والإفلات.

#### الخصائص (Props)

```typescript
interface ToolbarSortingProps<T> {
  items: Array<{
    id: keyof T // مفتاح الحقل
    label: string // العنوان المعروض
  }>
  className?: string
}
```

#### مثال الاستخدام

```tsx
<ToolbarSorting
  items={[
    { accessorKey: "name", label: "الاسم" },
    { accessorKey: "createdAt", label: "تاريخ الإنشاء" },
    { accessorKey: "email", label: "البريد الإلكتروني" },
  ]}
/>
```

### 4. ToolbarFilter (التصفية)

مكون التصفية المتقدم الذي يدعم أنواع مختلفة من الفلاتر.

#### الخصائص (Props)

```typescript
interface ToolbarFilterProps<T> {
  items: FilterItem<T>[] // قائمة عناصر التصفية
  shallow?: boolean // تحديث URL بدون إعادة تحميل (افتراضي: false)
  debounceMs?: number // تأخير التحديث (افتراضي: 300)
  className?: string
}
```

#### أنواع الفلاتر المدعومة

##### 1. فلتر النص (Text Filter)

```tsx
{
  variant: "text",
  accessorKey: "name",
  label: "الاسم",
  placeholder: "أدخل الاسم..."
}
```

##### 2. فلتر الأرقام (Number Filter)

```tsx
{
  variant: "number",
  accessorKey: "age",
  label: "العمر",
  range: { min: 0, max: 100 },
  step: 1,
  unit: "سنة"
}
```

##### 3. فلتر التاريخ (Date Filter)

```tsx
{
  variant: "date",
  accessorKey: "createdAt",
  label: "تاريخ الإنشاء",
  range: {
    min: "2020-01-01",
    max: "2024-12-31"
  }
}
```

##### 4. فلتر منطقي (Boolean Filter)

```tsx
{
  variant: "boolean",
  accessorKey: "isActive",
  label: "نشط"
}
```

##### 5. فلتر الاختيار الواحد (Select Filter)

```tsx
{
  variant: "select",
  accessorKey: "status",
  label: "الحالة",
  options: [
    { label: "نشط", value: "active" },
    { label: "غير نشط", value: "inactive" }
  ]
}
```

##### 6. فلتر الاختيار المتعدد (Multi-Select Filter)

```tsx
{
  variant: "multiSelect",
  accessorKey: "tags",
  label: "العلامات",
  options: [
    { label: "مهم", value: "important", count: 5 },
    { label: "عاجل", value: "urgent", count: 3 }
  ]
}
```

#### مثال شامل للتصفية

```tsx
<ToolbarFilter
  items={[
    {
      variant: "text",
      accessorKey: "name",
      label: "الاسم",
      placeholder: "بحث بالاسم...",
    },
    {
      variant: "select",
      accessorKey: "status",
      label: "الحالة",
      options: [
        { label: "نشط", value: "active" },
        { label: "غير نشط", value: "inactive" },
      ],
    },
    {
      variant: "date",
      accessorKey: "createdAt",
      label: "تاريخ الإنشاء",
    },
  ]}
  debounceMs={400}
/>
```

## دالة getToolbarQueryParser

دالة مساعدة تحول معاملات URL إلى استعلامات Prisma قابلة للتنفيذ.

### الوظيفة

تقوم هذه الدالة بتحليل معاملات البحث من URL وتحويلها إلى كائن يحتوي على:

- `where`: شروط التصفية والبحث بصيغة Prisma
- `orderBy`: شروط الترتيب بصيغة Prisma

### الاستخدام

```typescript
import { getToolbarQueryParser } from "@/components/toolbar"

// في Server Component
async function getData(searchParams: Promise<SearchParams>) {
  const { where, orderBy } = await getToolbarQueryParser(searchParams)

  return prisma.user.findMany({
    where,
    orderBy,
  })
}
```

### أمثلة التحويل

#### مثال 1: البحث النصي

```
URL: ?search={"accessorKey":"name","value":"أحمد"}
```

```typescript
// النتيجة:
{
  where: {
    name: { contains: "أحمد", mode: "insensitive" }
  },
  orderBy: []
}
```

#### مثال 2: التصفية والترتيب

```
URL: ?filters=[{"variant":"select","accessorKey":"status","value":"active"}]&sort=[{"accessorKey":"createdAt","value":"desc"}]
```

```typescript
// النتيجة:
{
  where: {
    AND: [
      { status: "active" }
    ]
  },
  orderBy: [
    { createdAt: "desc" }
  ]
}
```

#### مثال 3: فلاتر متعددة مع ربط OR

```
URL: ?filters=[...]&join=OR
```

```typescript
// النتيجة:
{
  where: {
    OR: [
      { name: { contains: "أحمد", mode: "insensitive" } },
      { email: { contains: "test", mode: "insensitive" } }
    ]
  },
  orderBy: []
}
```

## مثال شامل

```tsx
import { useTransition } from "react"

import {
  getToolbarQueryParser,
  Toolbar,
  ToolbarFilter,
  ToolbarSearch,
  ToolbarSorting,
} from "@/components/toolbar"

// في الصفحة
export default async function UsersPage({
  searchParams,
}: {
  searchParams: Promise<SearchParams>
}) {
  const { where, orderBy } = await getToolbarQueryParser(searchParams)
  const users = await prisma.user.findMany({ where, orderBy })

  return <UsersTable users={users} />
}

// في المكون
function UsersTable({ users }) {
  const [isPending, startTransition] = useTransition()

  return (
    <div>
      <Toolbar startTransition={startTransition}>
        <ToolbarSearch item={{ accessorKey: "name", placeholder: "بحث بالاسم..." }} />

        <ToolbarFilter
          items={[
            {
              variant: "text",
              accessorKey: "email",
              label: "البريد الإلكتروني",
            },
            {
              variant: "select",
              accessorKey: "role",
              label: "الدور",
              options: [
                { label: "مدير", value: "admin" },
                { label: "مستخدم", value: "user" },
              ],
            },
          ]}
        />

        <ToolbarSorting
          items={[
            { accessorKey: "name", label: "الاسم" },
            { accessorKey: "createdAt", label: "تاريخ الإنشاء" },
          ]}
        />
      </Toolbar>

      {/* جدول البيانات */}
      <DataTable data={users} />
    </div>
  )
}
```

## ملاحظات مهمة

1. **تزامن URL**: جميع التغييرات تنعكس في URL تلقائياً
2. **الأداء**: استخدم `debounceMs` لتقليل عدد الطلبات
3. **التوطين**: يدعم العربية والإنجليزية
4. **إمكانية الوصول**: مصمم وفقاً لمعايير الوصول
5. **TypeScript**: دعم كامل للأنواع المطبوعة

## نصائح للاستخدام

- **استخدم `startTransition`**: لتحسين تجربة المستخدم أثناء التحديثات
- **اضبط `debounceMs`**: حسب احتياجاتك (300ms للاستجابة السريعة، 500ms+ للتطبيقات البطيئة)
- **استخدم `shallow={true}`**: إذا كنت لا تريد إعادة تحميل الصفحة
- **تطابق الأسماء**: تأكد من تطابق `accessorKey` في عناصر التصفية مع أسماء الحقول في قاعدة البيانات
- **معالجة الأخطاء**: تأكد من معالجة الأخطاء في دالة `getData` عند استخدام `getToolbarQueryParser`

## الميزات الرئيسية

✅ **تزامن URL تلقائي** - جميع التغييرات تنعكس في الرابط
✅ **دعم TypeScript كامل** - أنواع مطبوعة آمنة
✅ **أداء محسن** - مع debouncing وcaching
✅ **إمكانية الوصول** - متوافق مع معايير WCAG
✅ **متعدد اللغات** - دعم العربية والإنجليزية
✅ **مرونة عالية** - قابل للتخصيص بالكامل
